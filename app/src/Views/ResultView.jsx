import React, { useState, useEffect, useRef } from 'react';
import ReactToPrint from 'react-to-print';
import { FaPrint, FaTrophy, FaMapMarkerAlt, FaUser } from 'react-icons/fa';

function ResultView({ contest, results }) {
    console.log('result view', contest)
    const [heading, setHeading] = useState(false);
    const [title, setTitle] = useState();

    const componentRef = useRef();

    useEffect(() => {
        let year = new Date().getFullYear();
        let newTitle;
        if (contest.title.includes('Honors')) {
            newTitle = contest.title;
            setHeading(`${year} Mark of Excellence`);
        } else {
            newTitle = `${year} ${contest.title}`;
            setHeading(false);
        }
        setTitle(newTitle);
    }, [contest.title]);

    function adjustFontSizeForPrint() {
        const ensembleElements = document.querySelectorAll('.flex-cell.ensembleName');

        ensembleElements.forEach(elem => {
            let fontSize;
            if (elem.textContent.length > 25) {
                fontSize = '0.95em';
            } else if (elem.textContent.length > 15) {
                fontSize = '0.98em';
            } else {
                fontSize = '1em';  // default size
            }
            elem.style.fontSize = fontSize;

            // Apply the same font size to corresponding director and location
            const parentRow = elem.closest('.flex-row');
            if (parentRow) {
                const directorElem = parentRow.querySelector('.flex-cell.director');
                const locationElem = parentRow.querySelector('.flex-cell.location');
                if (directorElem) directorElem.style.fontSize = fontSize;
                if (locationElem) locationElem.style.fontSize = fontSize;
            }
        });
    }

    function resetFontSizeAfterPrint() {
        const ensembleElements = document.querySelectorAll('.flex-cell.ensembleName');
        ensembleElements.forEach(elem => {
            elem.style.fontSize = '';  // reset to default size
        });
    }

    // Print View Component (hidden by default, shown only when printing)
    const PrintView = () => (
        <div className="print-container" ref={componentRef}>
            {heading && <h1 className='contest-title'>{heading}</h1>}
            <h1 className="contest-title">{title}</h1>

            {results && Object.keys(results).map((key) => {
                const classificationResults = results[key];
                if(key === 'State'){
                    key = 'State Level'
                }
                if (classificationResults.some(classificationData => classificationData.winners.length > 0)) {
                    return (
                        <div key={key} className="awardGrouping">
                            <h2 className='awardType'>{key} Winners</h2>
                            <br />
                            {classificationResults.map((classificationData, index) => {
                                if (classificationData.winners.length > 0) {
                                    if (key !== 'State Level') {
                                        return (
                                            <div key={index}>
                                                <h4 style={{ fontWeight: "bold" }}>{classificationData.classification}</h4>
                                                {classificationData.winners.map((winner, wIndex) => (
                                                    <div className="flex-row" key={wIndex}>
                                                        <div className="flex-cell ensembleName">{winner.ensembleName}</div>
                                                        <div className="flex-cell director">{winner.director}</div>
                                                        <div className="flex-cell location">{winner.city}{winner.state ? `, ${winner.state}` : ''}</div>
                                                    </div>
                                                ))}
                                                <br />
                                            </div>
                                        );
                                    } else {
                                        // For 'state', render winners without extra wrapping divs or spacing
                                        return classificationData.winners.map((winner, wIndex) => (
                                            <div className="flex-row" key={wIndex}>
                                                <div className="flex-cell ensembleName">{winner.ensembleName}</div>
                                                <div className="flex-cell director">{winner.director}</div>
                                                <div className="flex-cell location">{winner.city}{winner.state ? `, ${winner.state}` : ''}</div>
                                            </div>
                                        ));
                                    }
                                }
                                return null;
                            })}
                        </div>
                    );
                }
                return null;
            })}
        </div>
    );

    // Modern Display View Component
    const DisplayView = () => (
        <div className="modern-results-container px-4 py-6 max-w-7xl mx-auto">
            {/* Header Section */}
            <div className="text-center mb-8">
                {heading && (
                    <h1 className="text-2xl md:text-3xl font-bold text-header mb-2">
                        {heading}
                    </h1>
                )}
                <h1 className="text-3xl md:text-4xl font-bold text-iconGreen mb-6">
                    {title}
                </h1>

                {/* Print Button */}
                <div className="flex justify-center mb-6">
                    <ReactToPrint
                        trigger={() => (
                            <button className="flex items-center gap-2 bg-iconGreen hover:bg-green-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200 shadow-lg">
                                <FaPrint size={18} />
                                Print Results
                            </button>
                        )}
                        content={() => componentRef.current}
                        onBeforeGetContent={adjustFontSizeForPrint}
                        onAfterPrint={resetFontSizeAfterPrint}
                    />
                </div>
            </div>

            {/* Results Section */}
            <div className="space-y-8">
                {results && Object.keys(results).map((key) => {
                    const classificationResults = results[key];
                    const displayKey = key === 'State' ? 'State Level' : key;

                    if (classificationResults.some(classificationData => classificationData.winners.length > 0)) {
                        return (
                            <div key={key} className="bg-white rounded-xl shadow-lg overflow-hidden">
                                {/* Award Type Header */}
                                <div className="bg-gradient-to-r from-iconGreen to-green-600 px-6 py-4">
                                    <h2 className="text-xl md:text-2xl font-bold text-white flex items-center gap-3">
                                        <FaTrophy className="text-yellow-300" />
                                        {displayKey} Winners
                                    </h2>
                                </div>

                                {/* Winners Content */}
                                <div className="p-6">
                                    {classificationResults.map((classificationData, index) => {
                                        if (classificationData.winners.length > 0) {
                                            return (
                                                <div key={index} className="mb-8 last:mb-0">
                                                    {/* Classification Header (only for non-State Level) */}
                                                    {displayKey !== 'State Level' && (
                                                        <h3 className="text-lg md:text-xl font-semibold text-header mb-4 pb-2 border-b-2 border-dullGray">
                                                            {classificationData.classification}
                                                        </h3>
                                                    )}

                                                    {/* Winners Grid */}
                                                    <div className="grid gap-4 md:gap-6">
                                                        {classificationData.winners.map((winner, wIndex) => (
                                                            <div
                                                                key={wIndex}
                                                                className="bg-gradient-to-r from-gray-50 to-white border border-listBorderColor rounded-lg p-4 md:p-6 hover:shadow-md transition-shadow duration-200"
                                                            >
                                                                <div className="grid grid-cols-1 lg:grid-cols-12 gap-4">
                                                                    {/* Ensemble Name */}
                                                                    <div className="lg:col-span-4">
                                                                        <p className="text-lg font-bold text-header leading-tight break-words">
                                                                            {winner.ensembleName}
                                                                        </p>
                                                                    </div>

                                                                    {/* Director */}
                                                                    <div className="lg:col-span-5">
                                                                        <p className="text-base font-semibold text-header leading-tight break-words">
                                                                            {winner.director}
                                                                        </p>
                                                                    </div>

                                                                    {/* Location */}
                                                                    <div className="lg:col-span-3">
                                                                        <p className="text-base font-semibold text-header leading-tight break-words">
                                                                            {winner.city}{winner.state ? `, ${winner.state}` : ''}
                                                                        </p>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        ))}
                                                    </div>
                                                </div>
                                            );
                                        }
                                        return null;
                                    })}
                                </div>
                            </div>
                        );
                    }
                    return null;
                })}
            </div>
        </div>
    );

    return (
        <div className="result-container">
            <PrintView />
            <DisplayView />
        </div>
    );
}

export default ResultView;
